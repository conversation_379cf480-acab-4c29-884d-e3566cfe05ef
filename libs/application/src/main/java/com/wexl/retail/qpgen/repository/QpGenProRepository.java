package com.wexl.retail.qpgen.repository;

import com.wexl.retail.qpgen.model.QPGenPro;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface QpGenProRepository extends JpaRepository<QPGenPro, Long> {
  List<QPGenPro> findByOrgSlugOrderByIdDesc(String orgSlug);

  Optional<QPGenPro> findByTestDefinitionId(Long testDefinitionId);

  Optional<QPGenPro> findByIdAndOrgSlug(Long id, String orgSlug);
}
