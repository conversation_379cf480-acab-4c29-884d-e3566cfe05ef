package com.wexl.retail.qpgen.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.qpgen.dto.*;
import com.wexl.retail.qpgen.model.BluePrint;
import com.wexl.retail.qpgen.model.BluePrintSections;
import com.wexl.retail.qpgen.model.QPGenPro;
import com.wexl.retail.qpgen.repository.QpGenProRepository;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.ValidationUtils;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QpGenProV2Service {

  private final ValidationUtils validationUtils;
  private final BluePrintService bluePrintService;
  private final ContentService contentService;
  private final AuthService authService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final QpGenProRepository qpGenProRepository;
  private final QpGenProService qpGenProService;

  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionSummary(
      String orgSlug, QPGenProV2Dto.QuestionSummaryRequest request) {
    validationUtils.isOrgValid(orgSlug);
    var bluePrint = bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList = new ArrayList<>();
    var chaptersList =
        contentService.getChaptersByBoardGradeAndSubject(
            orgSlug, request.boardSlug(), request.gradeSlug(), request.subjectSlug());
    var bluePrintSections = bluePrint.getBluePrintSections();
    bluePrintSections.forEach(
        section ->
            summaryResponseList.add(
                QPGenProV2Dto.QuestionSummaryResponse.builder()
                    .sectionName(section.getSectionName())
                    .sectionsResponses(
                        buildSectionResponse(request, orgSlug, section, chaptersList))
                    .build()));
    return mergeDuplicateSectionsByName(summaryResponseList, bluePrintSections);
  }

  private List<QPGenProV2Dto.QuestionSummaryResponse> mergeDuplicateSectionsByName(
      List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList,
      List<BluePrintSections> bluePrintSections) {
    List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponsesList = new ArrayList<>();
    var sectionNames =
        bluePrintSections.stream().map(BluePrintSections::getSectionName).distinct().toList();
    sectionNames.forEach(
        name -> {
          var data =
              summaryResponseList.stream().filter(x -> x.sectionName().equals(name)).toList();
          summaryResponsesList.add(
              QPGenProV2Dto.QuestionSummaryResponse.builder()
                  .sectionName(name)
                  .sectionsResponses(modifyResponse(data))
                  .build());
        });

    return summaryResponsesList;
  }

  private List<QPGenProV2Dto.SectionsResponse> modifyResponse(
      List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList) {
    List<QPGenProV2Dto.SectionsResponse> responses = new ArrayList<>();
    summaryResponseList.forEach(
        summaryResponse -> responses.addAll(summaryResponse.sectionsResponses()));
    return responses;
  }

  private List<QPGenProV2Dto.SectionsResponse> buildSectionResponse(
      QPGenProV2Dto.QuestionSummaryRequest request,
      String orgSlug,
      BluePrintSections section,
      List<ChapterResponse> chaptersList) {

    List<QPGenProV2Dto.SectionsResponse> responses = new ArrayList<>();
    List<Integer> questionsRequestedList = getQuestionsRequested(request, section);
    List<String> chapterSlugs = request.chapterSlug();

    for (int i = 0; i < chapterSlugs.size(); i++) {
      String slug = chapterSlugs.get(i);
      var chapterData =
          chaptersList.stream().filter(x -> x.getChapterSlug().equals(slug)).findFirst();
      if (chapterData.isPresent()) {
        var contentRequest = buildContentRequest(section, chapterData.get(), request);
        var contentData = contentService.getQuestionsByChapterTags(contentRequest, orgSlug);
        responses.add(
            buildResponse(contentData, chapterData.get(), section, questionsRequestedList.get(i)));
      }
    }
    return responses;
  }

  private List<Integer> getQuestionsRequested(
      QPGenProV2Dto.QuestionSummaryRequest request, BluePrintSections section) {
    List<Integer> questionsRequested = new ArrayList<>();
    int totalQuestionCount = Math.toIntExact(section.getQuestionCount());
    int totalChapters = request.chapterSlug().size();
    if (totalQuestionCount != 0 && totalChapters > 0) {
      if (totalChapters == 1) {
        questionsRequested.add((int) Math.ceil(section.getQuestionCount()));
      } else {
        int baseQuestionCount = totalQuestionCount / totalChapters;
        int remainingQuestions = totalQuestionCount % totalChapters;

        for (int i = 0; i < totalChapters; i++) {
          if (i < remainingQuestions) {
            questionsRequested.add(baseQuestionCount + 1);
          } else {
            questionsRequested.add(baseQuestionCount);
          }
        }
      }
    }

    return questionsRequested;
  }

  private QPGenProV2Dto.SectionsResponse buildResponse(
      List<ContentQuestionsResponse> contentData,
      ChapterResponse chapterResponse,
      BluePrintSections section,
      long questionsRequested) {
    long totalQuestions = 0;
    if (!contentData.isEmpty()) {
      totalQuestions = contentData.size();
    }
    return QPGenProV2Dto.SectionsResponse.builder()
        .sectionId(section.getId())
        .sectionName(section.getSectionName())
        .chapterName(chapterResponse.getName())
        .chapterSlug(chapterResponse.getChapterSlug())
        .subjectName(chapterResponse.getSubjectName())
        .subjectSlug(chapterResponse.getSubjectSlug())
        .questionType(section.getType().name())
        .questionTagSlug(section.getTags())
        .questionComplexityName(section.getComplexity())
        .questionComplexitySlug(section.getComplexity())
        .selectedChapterQuestions(questionsRequested)
        .totalAvailableQuestions(totalQuestions)
        .build();
  }

  private BluePrintDto.ContentRequestForQuestionTags buildContentRequest(
      BluePrintSections section,
      ChapterResponse chapterData,
      QPGenProV2Dto.QuestionSummaryRequest request) {
    return BluePrintDto.ContentRequestForQuestionTags.builder()
        .subjectSlug(request.subjectSlug())
        .chapterName(chapterData.getName())
        .chapterSlug(chapterData.getChapterSlug())
        .questionType(section.getType().name())
        .questionTags(section.getTags())
        .complexity(section.getComplexity())
        .build();
  }

  public void saveQpGenProV2(String orgSlug, QPGenProV2Dto.Request request) {
    validationUtils.isOrgValid(orgSlug);
    bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    QPGenPro qpGen = buildAndSaveQpGenPro(orgSlug, request);
    TestDefinition testDefinition = createAndSaveTestDefinition(request, orgSlug, qpGen);
    updateQpGen(qpGen, testDefinition, request.questionSummary());
  }

  private QPGenPro buildAndSaveQpGenPro(String orgSlug, QPGenProV2Dto.Request request) {
    QPGenPro qpGen = buildQpGen(orgSlug, request);
    qpGen.setStatus(QpGenStatus.IN_PROGRESS);
    return qpGenProRepository.save(qpGen);
  }

  private QpGenProDto.Return updateQpGen(
      QPGenPro qpGen,
      TestDefinition testDefinition,
      List<QPGenProV2Dto.QuestionSummaryResponse> questionSummaryResponses) {
    filterQuestionSummaryResponses(questionSummaryResponses);
    qpGen.setTestDefinitionId(testDefinition.getId());
    qpGen.setStatus(QpGenStatus.DRAFT);
    qpGen.setMarks(testDefinition.getTotalMarks().longValue());
    qpGen.setSummary(questionSummaryResponses);
    var qpGenPro = qpGenProRepository.save(qpGen);
    return QpGenProDto.Return.builder().id(qpGenPro.getId()).build();
  }

  private void filterQuestionSummaryResponses(
      List<QPGenProV2Dto.QuestionSummaryResponse> questionSummaryResponses) {
    if (questionSummaryResponses == null) {
      return;
    }
    for (var response : questionSummaryResponses) {
      if (response.sectionsResponses() != null) {
        Map<String, QPGenProV2Dto.SectionsResponse> sectionMap = new HashMap<>();

        for (var section : response.sectionsResponses()) {
          String compositeKey =
              section.chapterSlug()
                  + "|"
                  + section.questionTagSlug()
                  + "|"
                  + section.questionComplexitySlug()
                  + "|"
                  + section.questionType();

          if (sectionMap.containsKey(compositeKey)) {
            QPGenProV2Dto.SectionsResponse existingSection = sectionMap.get(compositeKey);
            QPGenProV2Dto.SectionsResponse mergedSection =
                QPGenProV2Dto.SectionsResponse.builder()
                    .chapterSlug(existingSection.chapterSlug())
                    .chapterName(existingSection.chapterName())
                    .sectionName(existingSection.sectionName())
                    .sectionId(existingSection.sectionId())
                    .questionType(existingSection.questionType())
                    .questionComplexitySlug(existingSection.questionComplexitySlug())
                    .questionComplexityName(existingSection.questionComplexityName())
                    .questionTagSlug(existingSection.questionTagSlug())
                    .totalAvailableQuestions(
                        Optional.ofNullable(existingSection.totalAvailableQuestions()).orElse(0L))
                    .build();

            sectionMap.put(compositeKey, mergedSection);
          } else {
            sectionMap.put(compositeKey, section);
          }
        }
        response.sectionsResponses().clear();
        response.sectionsResponses().addAll(sectionMap.values());
      }
    }
  }

  private QPGenPro buildQpGen(String orgSlug, QPGenProV2Dto.Request request) {
    BluePrint bluePrint = bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    return QPGenPro.builder()
        .marks(request.marks())
        .duration(request.duration())
        .orgSlug(orgSlug)
        .status(QpGenStatus.IN_PROGRESS)
        .title(request.title())
        .boardSlug(request.boardSlug())
        .boardName(request.boardName())
        .gradeName(request.gradeName())
        .gradeSlug(request.gradeSlug())
        .subjectSlug(request.subjectSlug())
        .subjectName(request.subjectName())
        .chapterSlug(request.chapterSlug())
        .metaData(request.metaData())
        .chapterName(request.chapterName())
        .bluePrint(bluePrint)
        .createdByTeacher(authService.getUserDetails().getId())
        .build();
  }

  private TestDefinition createAndSaveTestDefinition(
      QPGenProV2Dto.Request request, String orgSlug, QPGenPro qpGen) {
    TestDefinition testDefinition = new TestDefinition();
    testDefinition.setBoardSlug(request.boardSlug());
    testDefinition.setGradeSlug(request.gradeSlug());
    testDefinition.setSubjectSlug(request.subjectSlug());
    testDefinition.setCategory(TestCategory.DEFAULT);
    testDefinition.setActive(Boolean.TRUE);
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setIsAutoEnabled(Boolean.TRUE);
    testDefinition.setOrganization(orgSlug);
    testDefinition.setTestName(request.title());
    testDefinition.setMessage(request.title());
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    testDefinition.setTestDefinitionSections(
        buildTestDefinitionSections(request, orgSlug, testDefinition, qpGen));
    testDefinition.setTeacher(authService.getTeacherDetails());
    testDefinition.setNoOfQuestions(getQuestionCount(testDefinition));
    testDefinition.setTotalMarks(request.marks().intValue());
    return testDefinitionRepository.save(testDefinition);
  }

  private int getQuestionCount(TestDefinition testDefinition) {
    return (int)
        testDefinition.getTestDefinitionSections().stream()
            .map(TestDefinitionSection::getTestQuestions)
            .mapToLong(List::size)
            .sum();
  }

  private List<TestDefinitionSection> buildTestDefinitionSections(
      QPGenProV2Dto.Request request, String orgSlug, TestDefinition td, QPGenPro qpGen) {

    List<TestDefinitionSection> sections = new ArrayList<>();
    List<QPGenProV2Dto.QuestionSummaryResponse> questionSummaries = request.questionSummary();

    long sequenceNumber = 1L;

    for (QPGenProV2Dto.QuestionSummaryResponse questions : questionSummaries) {
      var contentData = contentService.getQuestionsForQpGenProV2(questions, orgSlug);
      var bluePrintSections =
          qpGen.getBluePrint().getBluePrintSections().stream()
              .filter(x -> x.getSectionName().equals(questions.sectionName()))
              .toList();

      if (!contentData.isEmpty()) {
        TestDefinitionSection tds = new TestDefinitionSection();
        tds.setName(questions.sectionName());
        tds.setNoOfQuestions((long) contentData.size());
        tds.setSequenceNumber(sequenceNumber++);
        tds.setTestQuestions(buildTestQuestions(contentData, tds, bluePrintSections));
        tds.setTestDefinition(td);

        sections.add(tds);
      }
    }

    return sections;
  }

  private List<TestQuestion> buildTestQuestions(
      List<QPGenProV2Dto.QuestionsResponse> sectionData,
      TestDefinitionSection tds,
      List<BluePrintSections> bluePrintSections) {
    Map<String, Double> sectionMarksMap =
        bluePrintSections.stream()
            .collect(
                Collectors.toMap(
                    (blueprintSection) ->
                        blueprintSection.getTags() + "-" + blueprintSection.getComplexity(),
                    BluePrintSections::getMarks));
    List<TestQuestion> testQuestionList = new ArrayList<>();
    sectionData.forEach(
        question ->
            testQuestionList.add(
                TestQuestion.builder()
                    .marks(
                        Math.toIntExact(
                            sectionMarksMap
                                .get(question.questionTags() + "-" + question.complexitySlug())
                                .longValue()))
                    .questionUuid(question.uuid())
                    .type(question.type().toString())
                    .testDefinitionSection(tds)
                    .chapterSlug(question.chapterSlug())
                    .chapterName(question.chapterName())
                    .complexity(question.complexitySlug())
                    .category(question.categorySlug())
                    .questionTags(String.valueOf(question.questionTags()))
                    .mcqAnswer(
                        question.type().equals(QuestionType.MCQ)
                            ? Long.valueOf(question.answer())
                            : null)
                    .subjectiveAnswer(
                        question.type().equals(QuestionType.SUBJECTIVE) ? question.answer() : null)
                    .build()));
    return testQuestionList;
  }

  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionSummaryV2(Long qpGenProV2Id) {
    var qpGenPro = qpGenProService.validateQpGenPro(qpGenProV2Id);
    return qpGenPro.getSummary() == null ? new ArrayList<>() : qpGenPro.getSummary();
  }

  public void editQpGenProV2(String orgSlug, QPGenProV2Dto.Request request, Long qpGenProId) {
    var bluePrint = bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    QPGenPro qpGenPro = validateQpGenPro(qpGenProId);
    qpGenPro.setTitle(request.title());
    qpGenPro.setBluePrint(bluePrint);
    qpGenPro.setMarks(request.marks());
    qpGenPro.setDuration(request.duration());
    qpGenPro.setBoardSlug(request.boardSlug());
    qpGenPro.setBoardName(request.boardName());
    qpGenPro.setGradeSlug(request.gradeSlug());
    qpGenPro.setGradeName(request.gradeName());
    qpGenPro.setSubjectSlug(request.subjectSlug());
    qpGenPro.setSubjectName(request.subjectName());
    qpGenPro.setChapterSlug(request.chapterSlug());
    qpGenPro.setChapterName(request.chapterName());
    qpGenPro.setMetaData(request.metaData());
    qpGenPro.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    if (request.questionSummary() != null){
      TestDefinition testDefinition = updateTestDefinition(request, orgSlug, qpGenPro);
      updateQpGen(qpGenPro, testDefinition, request.questionSummary());
    }
    qpGenProRepository.save(qpGenPro);
  }

  public void updateQuestionChapterSelections(String orgSlug, Long qpGenProId, QPGenProV2Dto.QuestionChapterSelectionRequest request) {
    validationUtils.isOrgValid(orgSlug);

    if (request == null || request.questionChapterSelections() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Question chapter selections request cannot be null");
    }

    QPGenPro qpGenPro = qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug)
        .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "QPGenPro not found with id: " + qpGenProId + " for org: " + orgSlug));

    // Validate question numbers are sequential starting from 0
    validateQuestionNumbers(request.questionChapterSelections());

    // Validate chapter selections
    validateChapterSelections(request.questionChapterSelections());

    // Group selections by section name
    Map<String, List<QPGenProV2Dto.QuestionChapterSelection>> selectionsBySection =
        request.questionChapterSelections().stream()
            .collect(Collectors.groupingBy(QPGenProV2Dto.QuestionChapterSelection::sectionName));

    // Convert to response format
    List<QPGenProV2Dto.QuestionChapterSelectionResponse> responses = selectionsBySection.entrySet().stream()
        .map(entry -> QPGenProV2Dto.QuestionChapterSelectionResponse.builder()
            .sectionName(entry.getKey())
            .questionChapterSelections(entry.getValue())
            .build())
        .collect(Collectors.toList());

    qpGenPro.setQuestionChapterSelections(responses);
    qpGenPro.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    qpGenProRepository.save(qpGenPro);
  }

  public List<QPGenProV2Dto.QuestionChapterSelectionResponse> getQuestionChapterSelections(String orgSlug, Long qpGenProId) {
    validationUtils.isOrgValid(orgSlug);
    QPGenPro qpGenPro = qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug)
        .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "QPGenPro not found with id: " + qpGenProId + " for org: " + orgSlug));

    return qpGenPro.getQuestionChapterSelections() != null ?
        qpGenPro.getQuestionChapterSelections() :
        Collections.emptyList();
  }

  private TestDefinition updateTestDefinition(
      QPGenProV2Dto.Request request, String orgSlug, QPGenPro qpGenPro) {
    var testDefinition = testDefinitionRepository.findById(qpGenPro.getTestDefinitionId()).orElseThrow();
    if(qpGenPro.getTestDefinitionId() == null){
      testDefinition.setTestName(request.title());
      testDefinition.setTotalMarks(request.marks().intValue());
      testDefinition.setNoOfQuestions(getQuestionCount(testDefinition));
      testDefinition.setBoardSlug(request.boardSlug());
      testDefinition.setGradeSlug(request.gradeSlug());
      testDefinition.setSubjectSlug(request.subjectSlug());
      testDefinition.setMessage(request.title());
      testDefinition.getTestDefinitionSections().clear();
      testDefinition.setTestDefinitionSections(buildTestDefinitionSections(request, orgSlug, testDefinition, qpGenPro));
      testDefinition.setNoOfQuestions(getQuestionCount(testDefinition));
    }
    else{
      testDefinition = createAndSaveTestDefinition(request, orgSlug, qpGenPro);
    }
    return testDefinitionRepository.save(testDefinition);
  }

  private QPGenPro validateQpGenPro(Long id) {
    return qpGenProRepository
        .findById(id)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.QpGenProNotFound"));
  }

  private void validateQuestionNumbers(List<QPGenProV2Dto.QuestionChapterSelection> selections) {
    if (selections == null || selections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Question chapter selections cannot be empty");
    }

    // Extract question numbers and sort them
    List<Integer> questionNumbers = selections.stream()
        .map(QPGenProV2Dto.QuestionChapterSelection::questionNumber)
        .filter(Objects::nonNull)
        .sorted()
        .collect(Collectors.toList());

    // Validate that question numbers start from 0 and are sequential
    for (int i = 0; i < questionNumbers.size(); i++) {
      if (!questionNumbers.get(i).equals(i)) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Question numbers must be sequential starting from 0. Found: " + questionNumbers);
      }
    }
  }

  private void validateChapterSelections(List<QPGenProV2Dto.QuestionChapterSelection> selections) {
    for (QPGenProV2Dto.QuestionChapterSelection selection : selections) {
      if (selection.questionNumber() == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Question number cannot be null");
      }
      if (selection.questionNumber() < 0) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Question number must be non-negative");
      }
      if (selection.sectionName() == null || selection.sectionName().trim().isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Section name cannot be null or empty");
      }
      if (selection.chapterSlug() == null || selection.chapterSlug().trim().isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Chapter slug cannot be null or empty");
      }
      if (selection.selectedChapterQuestions() != null && selection.selectedChapterQuestions() < 0) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Selected chapter questions count cannot be negative");
      }
      if (selection.totalAvailableQuestions() != null && selection.totalAvailableQuestions() < 0) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Total available questions count cannot be negative");
      }
    }
  }
}
