package com.wexl.retail.qpgen.service;

import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.qpgen.dto.QPGenProV2Dto;
import com.wexl.retail.qpgen.model.QPGenPro;
import com.wexl.retail.qpgen.repository.QpGenProRepository;
import com.wexl.retail.util.ValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class QpGenProV2ServiceQuestionChapterSelectionTest {

    @Mock
    private QpGenProRepository qpGenProRepository;

    @Mock
    private ValidationUtils validationUtils;

    @InjectMocks
    private QpGenProV2Service qpGenProV2Service;

    private QPGenPro mockQpGenPro;
    private String orgSlug = "test-org";
    private Long qpGenProId = 1L;

    @BeforeEach
    void setUp() {
        mockQpGenPro = QPGenPro.builder()
            .id(qpGenProId)
            .orgSlug(orgSlug)
            .title("Test QPGenPro")
            .build();
    }

    @Test
    void testUpdateQuestionChapterSelections_Success() {
        // Arrange
        List<QPGenProV2Dto.QuestionChapterSelection> selections = Arrays.asList(
            QPGenProV2Dto.QuestionChapterSelection.builder()
                .questionNumber(0)
                .sectionName("Section A")
                .sectionId(1L)
                .chapterName("Chapter 1")
                .chapterSlug("chapter-1")
                .questionComplexityName("Easy")
                .questionComplexitySlug("easy")
                .questionTagSlug("algebra")
                .questionType("MCQ")
                .selectedChapterQuestions(5L)
                .totalAvailableQuestions(10L)
                .build(),
            QPGenProV2Dto.QuestionChapterSelection.builder()
                .questionNumber(1)
                .sectionName("Section A")
                .sectionId(1L)
                .chapterName("Chapter 2")
                .chapterSlug("chapter-2")
                .questionComplexityName("Medium")
                .questionComplexitySlug("medium")
                .questionTagSlug("geometry")
                .questionType("MSQ")
                .selectedChapterQuestions(3L)
                .totalAvailableQuestions(8L)
                .build()
        );

        QPGenProV2Dto.QuestionChapterSelectionRequest request = 
            QPGenProV2Dto.QuestionChapterSelectionRequest.builder()
                .questionChapterSelections(selections)
                .build();

        when(qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug))
            .thenReturn(Optional.of(mockQpGenPro));
        when(qpGenProRepository.save(any(QPGenPro.class))).thenReturn(mockQpGenPro);

        // Act
        assertDoesNotThrow(() -> 
            qpGenProV2Service.updateQuestionChapterSelections(orgSlug, qpGenProId, request)
        );

        // Assert
        verify(validationUtils).isOrgValid(orgSlug);
        verify(qpGenProRepository).findByIdAndOrgSlug(qpGenProId, orgSlug);
        verify(qpGenProRepository).save(any(QPGenPro.class));
    }

    @Test
    void testUpdateQuestionChapterSelections_QPGenProNotFound() {
        // Arrange
        QPGenProV2Dto.QuestionChapterSelectionRequest request = 
            QPGenProV2Dto.QuestionChapterSelectionRequest.builder()
                .questionChapterSelections(Collections.emptyList())
                .build();

        when(qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug))
            .thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () ->
            qpGenProV2Service.updateQuestionChapterSelections(orgSlug, qpGenProId, request)
        );

        assertTrue(exception.getMessage().contains("QPGenPro not found"));
        verify(qpGenProRepository, never()).save(any());
    }

    @Test
    void testUpdateQuestionChapterSelections_InvalidQuestionNumbers() {
        // Arrange
        List<QPGenProV2Dto.QuestionChapterSelection> selections = Arrays.asList(
            QPGenProV2Dto.QuestionChapterSelection.builder()
                .questionNumber(0)
                .sectionName("Section A")
                .chapterSlug("chapter-1")
                .build(),
            QPGenProV2Dto.QuestionChapterSelection.builder()
                .questionNumber(2) // Invalid: should be 1
                .sectionName("Section A")
                .chapterSlug("chapter-2")
                .build()
        );

        QPGenProV2Dto.QuestionChapterSelectionRequest request = 
            QPGenProV2Dto.QuestionChapterSelectionRequest.builder()
                .questionChapterSelections(selections)
                .build();

        when(qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug))
            .thenReturn(Optional.of(mockQpGenPro));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () ->
            qpGenProV2Service.updateQuestionChapterSelections(orgSlug, qpGenProId, request)
        );

        assertTrue(exception.getMessage().contains("Question numbers must be sequential"));
        verify(qpGenProRepository, never()).save(any());
    }

    @Test
    void testGetQuestionChapterSelections_Success() {
        // Arrange
        List<QPGenProV2Dto.QuestionChapterSelectionResponse> expectedResponses = Arrays.asList(
            QPGenProV2Dto.QuestionChapterSelectionResponse.builder()
                .sectionName("Section A")
                .questionChapterSelections(Collections.emptyList())
                .build()
        );

        mockQpGenPro.setQuestionChapterSelections(expectedResponses);
        when(qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug))
            .thenReturn(Optional.of(mockQpGenPro));

        // Act
        List<QPGenProV2Dto.QuestionChapterSelectionResponse> result = 
            qpGenProV2Service.getQuestionChapterSelections(orgSlug, qpGenProId);

        // Assert
        assertEquals(expectedResponses, result);
        verify(validationUtils).isOrgValid(orgSlug);
        verify(qpGenProRepository).findByIdAndOrgSlug(qpGenProId, orgSlug);
    }

    @Test
    void testGetQuestionChapterSelections_EmptyWhenNull() {
        // Arrange
        mockQpGenPro.setQuestionChapterSelections(null);
        when(qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug))
            .thenReturn(Optional.of(mockQpGenPro));

        // Act
        List<QPGenProV2Dto.QuestionChapterSelectionResponse> result = 
            qpGenProV2Service.getQuestionChapterSelections(orgSlug, qpGenProId);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testUpdateQuestionChapterSelections_NullRequest() {
        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () ->
            qpGenProV2Service.updateQuestionChapterSelections(orgSlug, qpGenProId, null)
        );

        assertTrue(exception.getMessage().contains("cannot be null"));
    }

    @Test
    void testUpdateQuestionChapterSelections_InvalidChapterSlug() {
        // Arrange
        List<QPGenProV2Dto.QuestionChapterSelection> selections = Arrays.asList(
            QPGenProV2Dto.QuestionChapterSelection.builder()
                .questionNumber(0)
                .sectionName("Section A")
                .chapterSlug("") // Invalid: empty
                .build()
        );

        QPGenProV2Dto.QuestionChapterSelectionRequest request = 
            QPGenProV2Dto.QuestionChapterSelectionRequest.builder()
                .questionChapterSelections(selections)
                .build();

        when(qpGenProRepository.findByIdAndOrgSlug(qpGenProId, orgSlug))
            .thenReturn(Optional.of(mockQpGenPro));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () ->
            qpGenProV2Service.updateQuestionChapterSelections(orgSlug, qpGenProId, request)
        );

        assertTrue(exception.getMessage().contains("Chapter slug cannot be null or empty"));
    }
}
